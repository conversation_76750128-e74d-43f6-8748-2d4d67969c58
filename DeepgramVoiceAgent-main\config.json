{"type": "Settings", "audio": {"input": {"encoding": "mulaw", "sample_rate": 8000}, "output": {"encoding": "mulaw", "sample_rate": 8000, "container": "none"}}, "agent": {"language": "en", "listen": {"provider": {"type": "deepgram", "model": "nova-3", "keyterms": ["hello", "goodbye"]}}, "think": {"provider": {"type": "open_ai", "model": "gpt-4o-mini", "temperature": 0.7}, "prompt": "You are a professional pharmacy assistant. You can: 1) Get drug info with get_drug_info, 2) Place orders with place_order, 3) Look up orders with lookup_order. IMPORTANT: Always ask users to spell out their full name clearly when placing orders. Confirm all order details before processing - including customer name, drug name, and quantity. Be thorough and professional in collecting information. If a user provides a name that's unclear, ask them to spell it out letter by letter. Always confirm the complete order details before finalizing any transaction.", "functions": [{"name": "get_drug_info", "description": "Get detailed information about a specific drug. Use this function when: A customer asks about drug information, side effects, or pricing. A customer wants to know what a medication does. A customer asks questions like 'What is aspirin?' or 'Tell me about ibuprofen'. A customer wants to know drug prices or descriptions. Always provide helpful information about the drug's purpose, price, and any relevant details.", "parameters": {"type": "object", "properties": {"drug_name": {"type": "string", "description": "Name of the drug to look up. Convert to lowercase for matching. Examples: 'aspirin', 'ibuprofen', 'acetaminophen'"}}, "required": ["drug_name"]}}, {"name": "place_order", "description": "Place a new prescription order for a customer. Use this function when: A customer wants to order medication. A customer asks to refill a prescription. A customer says 'I need to order' or 'Can I get some'. Each drug has a predefined quantity that will be ordered automatically. Before placing an order: Verify the drug exists using get_drug_info. Get the customer's name for the order. Always confirm the order details before processing.", "parameters": {"type": "object", "properties": {"customer_name": {"type": "string", "description": "Customer's full name for the order. Use as provided by the customer."}, "drug_name": {"type": "string", "description": "Name of the drug to order. Convert to lowercase for matching. Must be a valid drug in our system. Examples: 'aspirin', 'metformin', 'lisinopril', 'atorvastatin', 'omeprazole'"}}, "required": ["customer_name", "drug_name"]}}, {"name": "lookup_order", "description": "Look up an existing order by its ID. Use this function when: A customer asks about a specific order. A customer wants to check order status. A customer says 'Where is my order' or 'Check order number'. A customer provides an order ID number. Always verify the order exists and provide clear information about the order details.", "parameters": {"type": "object", "properties": {"order_id": {"type": "integer", "description": "The order ID number to look up. Must be a valid order number in our system."}}, "required": ["order_id"]}}]}, "speak": {"provider": {"type": "deepgram", "model": "aura-2-thalia-en"}}, "greeting": "Hello! I'm your pharmacy assistant. I can help you with drug information, placing orders, and checking order status. When placing orders, I'll need your full name spelled out clearly and will confirm all details with you. How can I assist you today?"}}